export type TArticle = {
  title: string;
  slug: string;
  description: string | undefined;
  content: string | undefined;
  words: number | undefined;
  coverImage: string;
  status: "draft" | "staged" | "approved" | "published" | "deleted";
  group: string;
  authorId: string;
  publishedAt: number;
};

export type TMember = {
  avatarUrl: string | null | undefined;
  _creationTime: number;
  name?: string | undefined;
  image?: string | undefined;
  email?: string | undefined;
  phone?: string | undefined;
  username?: string | undefined;
  bio?: string | undefined;
  role?: "author" | "admin" | "media-manager" | "ads-manager" | undefined;
  coverImage?: string | undefined;
};
