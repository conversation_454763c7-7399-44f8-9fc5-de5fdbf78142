import { type ClassValue, clsx } from "clsx";
import {
  FaFacebook,
  FaGithub,
  FaInstagram,
  FaLinkedin,
  FaTiktok,
  FaTwitch,
} from "react-icons/fa";
import { FaXTwitter, FaYoutube } from "react-icons/fa6";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Estimate reading time based on word count
 * @param text - The article content (string)
 * @param wordsPerMinute - Optional: default is 200
 * @returns string like "2 min read"
 */
export function calculateReadingTimeFromWords(
  wordCount: number,
  wordsPerMinute = 200
): string {
  if (!wordCount || wordCount <= 0) {
    return "0 min read";
  }

  const minutes = wordCount / wordsPerMinute;

  if (minutes < 1) {
    return "Less than 1 min read";
  }

  return `${Math.ceil(minutes)} min read`;
}

export function getShortTimeAgo(date: Date | number): string {
  const now = Date.now();
  const diff = now - new Date(date).getTime(); // in milliseconds

  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const weeks = Math.floor(diff / (1000 * 60 * 60 * 24 * 7));
  const months = Math.floor(diff / (1000 * 60 * 60 * 24 * 30));
  const years = Math.floor(diff / (1000 * 60 * 60 * 24 * 365));

  if (seconds < 60) {
    return "Just now";
  }
  if (minutes < 60) {
    return `${minutes}min`;
  }
  if (hours < 24) {
    return `${hours}h `;
  }
  if (days < 7) {
    return `${days}d `;
  }
  if (weeks < 4) {
    return `${weeks}w `;
  }
  if (months < 12) {
    return `${months}mo `;
  }
  return `${years}y ago`;
}

export function capitalize(text: string): string {
  return text.length > 0 ? text.charAt(0).toUpperCase() + text.slice(1) : "-";
}

export function truncate(text: string, maxLength: number): string {
  return text.length > maxLength
    ? `${text.slice(0, maxLength)}...`
    : text || "-";
}

export const getSocialIcon = (name: string) => {
  switch (name) {
    case "twitter":
      return FaXTwitter;
    case "linkedin":
      return FaLinkedin;
    case "github":
      return FaGithub;
    case "facebook":
      return FaFacebook;
    case "instagram":
      return FaInstagram;
    case "youtube":
      return FaYoutube;
    case "twitch":
      return FaTwitch;
    case "tiktok":
      return FaTiktok;
    default:
      return null;
  }
};
