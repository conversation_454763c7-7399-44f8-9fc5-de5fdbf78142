import { isValidPhoneNumber } from "react-phone-number-input";
import z from "zod";
export const usernameFormSchema = z.object({
  username: z
    .string()
    .min(3)
    .max(32)
    .toLowerCase()
    .trim()
    .regex(
      /^[a-zA-Z0-9]+$/,
      "Username may only contain alphanumeric characters."
    ),
});

export type TUsernameFormValues = z.infer<typeof usernameFormSchema>;

export const displayNameFormSchema = z.object({
  name: z.string().min(3).max(32),
});
export type TDisplayNameFormValues = z.infer<typeof displayNameFormSchema>;

export const deleteUserFormSchema = z.object({
  title: z
    .string()
    .min(1, { message: "Please type 'delete my account' to confirm." })
    .refine((val) => val.trim().toLowerCase() === "delete my account", {
      message: "You must type exactly: delete my account",
    }),
});
export type TDeleteUserFormValues = z.infer<typeof deleteUserFormSchema>;

export const phoneFormSchema = z.object({
  phone: z
    .string()
    .min(1, "Phone number is required")
    .refine(isValidPhoneNumber, { message: "Invalid phone number" }),
});
export type TPhoneFormValues = z.infer<typeof phoneFormSchema>;

export const bioFormSchema = z.object({
  bio: z.string().min(10, "Bio must be at least 10 characters."),
});
export type TBioFormValues = z.infer<typeof bioFormSchema>;

export const coverImageFormSchema = z.object({
  coverImage: z.string().min(1),
});
export type TCoverImageFormValues = z.infer<typeof coverImageFormSchema>;

export const socialLinkFormSchema = z.object({
  name: z.string().min(1),
  url: z.url(),
});
export type TSocialLinkFormValues = z.infer<typeof socialLinkFormSchema>;
