import { api } from "./_generated/api";
import { httpAction } from "./_generated/server";

// get all published articles
export const getArticles = httpAction(async (ctx, req) => {
  // get key from headers
  const authHeader = req.headers.get("Authorization") || "";
  const key = authHeader.replace("Bearer ", "").trim();

  if (key !== process.env.ARTICLES_API_KEY) {
    return new Response(JSON.stringify({ message: "Unauthorized" }), {
      status: 401,
    });
  }
  if (!key) {
    return new Response(JSON.stringify({ message: "Unauthorized" }), {
      status: 401,
    });
  }
  const articles = await ctx.runQuery(api.articles.getPublishedArticles, {});
  return new Response(JSON.stringify(articles), {
    status: 200,
    headers: { "Content-Type": "application/json" },
  });
});

export const getTeamMembers = httpAction(async (ctx, req) => {
  const members = await ctx.runQuery(api.users.getAllUsers, {});
  return new Response(JSON.stringify(members), {
    status: 200,
    headers: { "Content-Type": "application/json" },
  });
});
