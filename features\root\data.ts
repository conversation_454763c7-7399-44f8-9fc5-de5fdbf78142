// import { members } from "@/config/web";
import type { TArticle, TMember } from "./types";

export async function getArticles(): Promise<TArticle[]> {
  const res = await fetch(`${process.env.CONVEX_SITE_URL}/articles`, {
    headers: {
      Authorization: `Bearer ${process.env.ARTICLES_API_KEY}`,
    },
  });
  const articles = await res.json();
  if (!articles) return [];
  return articles;
}

export async function getArticle(slug: string): Promise<TArticle | null> {
  // use filter
  const articles = await getArticles();
  const article = articles.find((art) => art.slug === slug);
  if (!article) return null;
  return article;
}

export async function getMembers(): Promise<TMember[]> {
  const res = await fetch(`${process.env.CONVEX_SITE_URL}/members`);
  const teamMembers = res.json();
  if (!teamMembers) return [];
  return teamMembers;
}

export async function getMember(username: string): Promise<TMember | null> {
  const teamMembers = await getMembers();
  const teamMember = teamMembers.find((member) => member.username === username);
  if (!teamMember) return null;
  return teamMember;
}

// if is media-manager return Media Manager , if is author return Author, if is admin return Admin, if is ads-manager return Ads Manager
export function getRoleName(role: TMember["role"]) {
  if (role === "media-manager") return "Media Manager";
  if (role === "author") return "Author";
  if (role === "admin") return "Admin";
  if (role === "ads-manager") return "Ads Manager";
  return "Unknown";
}

// export const articles = [
//   {
//     title: "Article 1",
//     slug: "article-1",
//     description: "This is the first article.",
//     content: "This is the content of the first article.",
//     words: 100,
//     coverImage: "/noCover_black.jpg",
//     status: "draft",
//     group: "sport",
//     userId: 1,
//     publishedAt: Date.now(),
//   },
//   {
//     title: "Article 2",
//     slug: "article-2",
//     description: "This is the second article.",
//     content: "This is the content of the second article.",
//     words: 200,
//     coverImage: "/noCover2_black.jpg",
//     status: "draft",
//     group: "culture",
//     userId: 1,
//     publishedAt: Date.now(),
//   },
// ];
