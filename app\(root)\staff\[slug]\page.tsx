import Image from 'next/image';
import { notFound } from 'next/navigation';
import { <PERSON>aLinkedin, FaYoutube } from 'react-icons/fa';
import { PageContainer } from '@/components/custom/page-container';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { getMember, getRoleName } from '@/features/root/data';
export default async function StaffPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const member = await getMember(slug);
  if (!member) {
    return notFound();
  }
  return (
    <PageContainer>
      <div className="group flex flex-col gap-10 overflow-hidden px-0 py-10 md:px-4 lg:px-6">
        <div className="flex flex-col gap-2 md:gap-4">
          <div className="relative">
            <Image
              alt="team member"
              className="h-[12rem] w-full rounded-md object-cover object-center grayscale transition-all duration-500 hover:grayscale-0 group-hover:rounded-xl md:h-[22.5rem]"
              height="1239"
              src={member.coverImage || '/images/member-one.webp'}
              width="826"
            />
            <div className="-bottom-[40%] -translate-x-1/2 -translate-y-1/2 absolute left-1/2 z-20 rounded-full bg-background p-1.5">
              <Avatar className="size-16 md:size-20 lg:size-30">
                <AvatarImage src={member.avatarUrl || '/profile.svg'} />
                <AvatarFallback>{member.username?.charAt(0)}</AvatarFallback>
              </Avatar>
            </div>
          </div>

          <div className="px-1 py-10 sm:py-0 md:px-2">
            <div className="flex justify-between">
              <h3 className="font-medium text-base transition-all duration-500 group-hover:tracking-wider">
                {member.name}
              </h3>
            </div>
            <div className="mt-1 flex items-center justify-between">
              <div className="flex flex-col gap-1">
                <span className="inline-block text-muted-foreground text-sm ">
                  {getRoleName(member.role)}
                </span>
                <span className="text-muted-foreground text-sm">
                  Joined {new Date(member._creationTime).getFullYear()}
                </span>
              </div>
              <a
                className="inline-block text-sm tracking-wide transition-all duration-500 hover:underline group-hover:text-primary-600 "
                href={member.email}
                rel="noopener noreferrer"
                target="_blank"
              >
                {' '}
                Linktree
              </a>
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-6 md:flex-row md:gap-14">
          <div className="flex-2">
            <p className="mt-6 text-pretty text-muted-foreground">
              {member.bio}
            </p>
          </div>
          <Card className="flex-1">
            <CardHeader>
              <CardTitle>Social Medias</CardTitle>
              <CardDescription>
                You can find me on social medias.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="flex flex-col gap-2">
                <li>
                  <a
                    className="group flex items-center gap-1 hover:underline"
                    href={member.email}
                    rel="noopener noreferrer"
                    target="_blank"
                  >
                    <FaLinkedin className="size-4" />
                    <span className="text-muted-foreground text-sm group-hover:text-primary">
                      Linkedin
                    </span>
                    <span className="text-muted-foreground text-sm">
                      /{member.username}
                    </span>
                  </a>
                </li>
                <li>
                  <a
                    className="group flex items-center gap-1 hover:underline"
                    href={member.email}
                    rel="noopener noreferrer"
                    target="_blank"
                  >
                    <FaYoutube className="size-4" />

                    <span className="text-muted-foreground text-sm group-hover:text-primary">
                      Youtube
                    </span>
                    <span className="text-muted-foreground text-sm">
                      /{member.username}
                    </span>
                  </a>
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <Button asChild>
                <a
                  className="w-full"
                  href={member.email}
                  rel="noopener noreferrer"
                  target="_blank"
                >
                  View all
                </a>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </PageContainer>
  );
}
